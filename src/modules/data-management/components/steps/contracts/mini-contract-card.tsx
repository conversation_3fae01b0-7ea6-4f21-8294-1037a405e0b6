import { CheckCircle, FileIcon } from 'lucide-react'
import Image from 'next/image'

import { cn } from '@/lib/utils'

import { Contract } from '../../../../contracts/libs/contract-types'

interface MiniContractCardProps {
  contract: Contract
  active?: boolean
  isUpdated: boolean
  onClick?: () => void
}

export function MiniContractCard({ contract, active, isUpdated, onClick }: MiniContractCardProps) {
  return (
    <button
      type="button"
      onClick={onClick}
      className={cn(
        'w-full flex flex-row items-center border rounded-xl px-4 py-3 transition-all duration-150 shadow-sm cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary/50 bg-white relative',
        {
          'border-primary bg-primary/10 shadow-md': active,
          'border-gray-200 hover:bg-gray-50': !active,
          'border-green-200 bg-green-50': isUpdated,
        }
      )}
      aria-pressed={active}
    >
      <div className="w-11 h-11 flex items-center justify-center bg-gray-100 rounded-lg mr-4 flex-shrink-0 overflow-hidden">
        {contract.provider_img_id ? (
          <Image
            src={contract.provider_img_id}
            alt={contract.provider_name}
            width={44}
            height={44}
            className="object-contain w-11 h-11"
          />
        ) : (
          <FileIcon className="w-7 h-7 text-gray-400" />
        )}
      </div>
      <div className="flex-1 flex flex-col min-w-0 items-start">
        <div className="flex flex-row items-center w-full mb-0.5">
          <span className="text-[11px] text-gray-400 truncate mr-2">ID: {contract.id}</span>
        </div>
        <span className="font-semibold text-sm text-gray-900 truncate max-w-full mb-0.5">{contract.category_name}</span>
        <span className="text-[12px] text-gray-500 truncate max-w-full">{contract.provider_name}</span>
      </div>

      {/* Checkmark indicator for updated contracts */}
      {isUpdated && (
        <div className="absolute -top-2 -right-2 bg-green-500 rounded-full p-1">
          <CheckCircle className="w-4 h-4 text-white" />
        </div>
      )}
    </button>
  )
}
