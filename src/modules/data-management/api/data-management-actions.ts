'use server'

import { manageClientDataManagementToken } from '@/modules/auth/actions/auth-actions'
import { revalidateTag } from 'next/cache'
import { z } from 'zod'

import { fetchApi } from '@/lib/fetch-api'
import { fetchAuthenticatedApi } from '@/lib/fetch-with-auth-api'
import { authenticatedAction } from '@/lib/safe-actions'

import { extractProfileSteps, hasProfileData, mapDataManagementToProfile } from '../libs/profile-mapper'
import { DATA_MANAGEMENT_TAGS } from './data-management-tags'

// Schemas
const dataManagementStepSchema = z.object({
  title: z.string(),
  isActive: z.boolean(),
  isLast: z.boolean(),
  isCompleted: z.boolean(),
})

const navigationStateSchema = z.object({
  currentStep: z.string(),
  navigationSteps: z.array(dataManagementStepSchema),
})

const clientDataStepsSchema = z.record(z.any()).optional()

const saveDataManagementSchema = z.object({
  formData: z.object({
    steps: clientDataStepsSchema,
    navigationState: navigationStateSchema,
  }),
  status: z.enum(['DRAFT', 'DONE']).optional(),
})

// Server Actions
export const saveDataManagementAction = authenticatedAction
  .schema(saveDataManagementSchema)
  .action(async ({ parsedInput: { formData, status }, ctx: { session } }) => {
    // Get user ID from session
    const userId = session?.user?.id
    if (!userId) {
      throw new Error('User ID not found in session')
    }

    // Use form data directly without transformation
    const directFormData = {
      steps: formData.steps || {},
      navigationState: formData.navigationState,
    }

    const clientDataManagementToken = await manageClientDataManagementToken({
      action: 'get',
    })

    // Prepare request body - only include status if it's "DONE"
    const requestBody: Record<string, unknown> = {
      formData: directFormData,
    }

    // Only include status field when it's explicitly "DONE" (final step completion)
    if (status === 'DONE') {
      requestBody.status = status
    }

    // Use the new backend API endpoint with authentication
    const request = await fetchAuthenticatedApi(`/client_data_managements/${clientDataManagementToken}`, {
      method: 'PUT',
      logCurl: true,
      newApi: true,
      body: JSON.stringify(requestBody),
    })

    if (request.error) {
      console.error('❌ [saveDataManagementAction] API request failed:', request.error)
      return {
        serverError: 'Failed to save data management form',
      }
    }

    revalidateTag(DATA_MANAGEMENT_TAGS.ALL)

    console.log('✅ [saveDataManagementAction] Save successful:', request.data)

    return request.data
  })

export const resetDataManagementAction = authenticatedAction.action(async () => {
  // Get client data management token
  const clientDataManagementToken = await manageClientDataManagementToken({
    action: 'get',
  })

  // put form data to be null
  const request = await fetchAuthenticatedApi(`/client_data_managements/${clientDataManagementToken}`, {
    method: 'PUT',
    newApi: true,
    body: JSON.stringify({ formData: null }),
  })

  if (request.error) {
    console.error('❌ [resetDataManagementAction] API request failed:', request.error)
    return {
      serverError: 'Failed to reset data management form',
    }
  }

  revalidateTag(DATA_MANAGEMENT_TAGS.ALL)

  return request.data
})

// Schema for two-step validation action
const twoStepValidationSchema = z.object({
  formData: z.object({
    steps: clientDataStepsSchema,
    navigationState: navigationStateSchema,
  }),
  status: z.enum(['DRAFT', 'DONE']).optional(),
})

/**
 * Two-step validation action that:
 * 1. First validates and updates profile data via profile API
 * 2. If profile update is successful, proceeds with data management update
 * 3. If profile update has validation errors, returns those errors without proceeding
 */
export const saveDataManagementWithProfileValidationAction = authenticatedAction
  .schema(twoStepValidationSchema)
  .action(async ({ parsedInput: { formData, status }, ctx: { session } }) => {
    console.log('🔄 [saveDataManagementWithProfileValidation] Starting two-step validation process')

    // Step 1: Extract and validate profile data
    const profileSteps = extractProfileSteps(formData.steps || {})

    if (hasProfileData(profileSteps)) {
      console.log('📋 [saveDataManagementWithProfileValidation] Profile data found, validating via profile API')

      try {
        // Transform data management steps to profile format
        const profileData = mapDataManagementToProfile(profileSteps)

        // Send profile data for validation via profile API
        const profileValidationResponse = await fetchApi(
          '/profile',
          {
            method: 'PUT',
            body: JSON.stringify({ profile: profileData }),
          },
          session
        )

        if (profileValidationResponse.error) {
          console.error(
            '❌ [saveDataManagementWithProfileValidation] Profile validation failed:',
            profileValidationResponse.error
          )

          // Return profile validation errors to prevent data management save
          return {
            serverError: 'Profile validation failed',
            profileValidationErrors: profileValidationResponse.error,
            profileValidationDetails: profileValidationResponse.details,
          }
        }

        console.log('✅ [saveDataManagementWithProfileValidation] Profile validation successful')
      } catch (error) {
        console.error('❌ [saveDataManagementWithProfileValidation] Profile validation error:', error)

        // Handle profile validation errors
        if (error instanceof Error) {
          return {
            serverError: 'Profile validation failed',
            profileValidationErrors: error.message,
          }
        }

        return {
          serverError: 'Profile validation failed with unknown error',
        }
      }
    }

    // Step 2: If profile validation passed (or no profile data), proceed with data management save
    console.log('💾 [saveDataManagementWithProfileValidation] Proceeding with data management save')

    const clientDataManagementToken = await manageClientDataManagementToken({
      action: 'get',
    })

    // Prepare request body - only include status if it's "DONE"
    const requestBody: Record<string, unknown> = {
      formData: {
        steps: formData.steps || {},
        navigationState: formData.navigationState,
      },
    }

    // Only include status field when it's explicitly "DONE" (final step completion)
    if (status === 'DONE') {
      requestBody.status = status
    }

    // Use the new backend API endpoint with authentication
    const request = await fetchAuthenticatedApi(`/client_data_managements/${clientDataManagementToken}`, {
      method: 'PUT',
      logCurl: true,
      newApi: true,
      body: JSON.stringify(requestBody),
    })

    if (request.error) {
      console.error('❌ [saveDataManagementWithProfileValidation] Data management save failed:', request.error)
      return {
        serverError: 'Failed to save data management form',
      }
    }

    revalidateTag(DATA_MANAGEMENT_TAGS.ALL)

    console.log('✅ [saveDataManagementWithProfileValidation] Two-step validation completed successfully')

    return request.data
  })

// Helper functions for two-step validation are imported from profile-data-mapper
