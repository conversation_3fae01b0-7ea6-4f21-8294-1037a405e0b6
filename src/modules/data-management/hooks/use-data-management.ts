'use client'

import { useMemo } from 'react'

import { useAction } from 'next-safe-action/hooks'
import { toast } from 'sonner'

import { saveDataManagementAction, saveDataManagementWithProfileValidationAction } from '../api/data-management-actions'
import { isProfileRelatedStep } from '../libs/profile-mapper'
import {
  ClientDataManagement,
  ClientDataSteps,
  DataManagementStepNames,
  NavigationState,
} from '../types/data-management-types'

export function useDataManagement(formData: ClientDataManagement) {
  const {
    execute: executeSaveData,
    isPending: isSaving,
    result: saveResult,
  } = useAction(saveDataManagementAction, {
    onSuccess: async (data) => {
      console.log('✅ [useDataManagement] Data saved successfully:', data)
      // The revalidateTag in the server action should trigger a refresh
      // of the parent component's data
    },
    onError: (error) => {
      console.error('❌ [useDataManagement] Failed to save data:', error)
      toast.error('Failed to save form data. Please try again.')
    },
  })

  // Two-step validation action for profile-related steps
  const {
    execute: executeSaveWithProfileValidation,
    isPending: isSavingWithValidation,
    result: saveWithValidationResult,
  } = useAction(saveDataManagementWithProfileValidationAction, {
    onSuccess: async (data) => {
      console.log('✅ [useDataManagement] Two-step validation completed successfully:', data)
      // The revalidateTag in the server action should trigger a refresh
      // of the parent component's data
    },
    onError: (error) => {
      console.error('❌ [useDataManagement] Two-step validation failed:', error)

      // Check if it's a profile validation error
      const errorMessage = error.error?.serverError || 'Unknown error'

      if (errorMessage.includes('Profile validation failed')) {
        toast.error('Profile validation failed', {
          description: 'Please check your profile data and try again.',
          duration: 8000,
        })
      } else {
        toast.error('Failed to save form data. Please try again.')
      }
    },
  })

  const currentNavigationState = formData?.navigationState

  /**
   * Save step data and automatically navigate to next step
   * This preserves the complete form structure and navigation state
   * Uses two-step validation for profile-related steps
   */
  const saveStepData = async (stepName: DataManagementStepNames, stepData: unknown) => {
    try {
      if (!currentNavigationState?.navigationSteps) {
        console.error('❌ [saveStepData] Navigation steps not available')
        return { success: false, error: 'Navigation state not available' }
      }

      // Prepare step data in the format expected by the API
      const formattedStepData: ClientDataSteps = {
        [stepName]: {
          ...(stepData as Record<string, unknown>),
          lastUpdate: new Date().toISOString(),
        },
      }

      // Update navigation state to mark current step as completed
      const updatedSteps = currentNavigationState.navigationSteps.map((step) => ({
        ...step,
        isCompleted: step.title === stepName ? true : step.isCompleted,
      }))

      // Find next step index
      const currentStepIndex = updatedSteps.findIndex((step) => step.title === stepName)
      const nextStepIndex = currentStepIndex + 1

      // Determine the current step - stay on current step if it's the last one
      const isLastStep = nextStepIndex >= updatedSteps.length
      const nextStepToSet = isLastStep ? stepName : updatedSteps[nextStepIndex].title

      // Update navigation steps with proper active state
      const finalUpdatedSteps = updatedSteps.map((step) => ({
        ...step,
        isActive: step.title === nextStepToSet,
      }))

      const updatedNavigationState: NavigationState = {
        currentStep: nextStepToSet,
        navigationSteps: finalUpdatedSteps,
      }

      const updatedFormData: ClientDataManagement = {
        steps: {
          ...formData.steps,
          ...formattedStepData,
        },
        navigationState: updatedNavigationState,
      }

      console.log('💾 [useDataManagement] Saving step data:', {
        stepName,
        nextStep: nextStepToSet,
        isLastStep,
        preservedStepsCount: finalUpdatedSteps.length,
        allSteps: Object.keys(updatedFormData.steps),
        isProfileRelated: isProfileRelatedStep(stepName),
      })

      // Use two-step validation for profile-related steps
      if (isProfileRelatedStep(stepName)) {
        console.log('🔄 [useDataManagement] Using two-step validation for profile-related step')
        executeSaveWithProfileValidation({
          formData: updatedFormData,
        })
      } else {
        console.log('💾 [useDataManagement] Using standard save for non-profile step')
        executeSaveData({
          formData: updatedFormData,
        })
      }

      return { success: true, nextStep: nextStepToSet }
    } catch (error) {
      console.error('❌ [useDataManagement] Error in saveStepData:', error)
      return { success: false, error }
    }
  }

  const goToNextStep = async (currentStepData?: Record<string, unknown>) => {
    try {
      if (!currentNavigationState?.navigationSteps) {
        console.error('❌ [goToNextStep] Navigation steps not available')
        return { success: false, error: 'Navigation state not available' }
      }

      const currentStepIndex = currentNavigationState.navigationSteps.findIndex(
        (step) => step.title === currentNavigationState.currentStep
      )

      const nextStepIndex = currentStepIndex + 1

      if (nextStepIndex >= currentNavigationState.navigationSteps.length) {
        console.log('📝 [useDataManagement] Already at last step')
        return { success: false, error: 'Already at last step' }
      }

      const nextStep = currentNavigationState.navigationSteps[nextStepIndex]

      // Update navigation state for next step
      const updatedSteps = currentNavigationState.navigationSteps.map((step, index) => ({
        ...step,
        isActive: index === nextStepIndex,
        isCompleted: index < nextStepIndex || step.isCompleted,
      }))

      const updatedNavigationState: NavigationState = {
        currentStep: nextStep.title,
        navigationSteps: updatedSteps,
      }

      console.log('➡️ [useDataManagement] Moving to next step:', {
        from: currentNavigationState.currentStep,
        to: nextStep.title,
        navigationState: updatedNavigationState,
      })

      // Save current step data if provided
      let updatedFormData = formData
      if (currentStepData) {
        const formattedStepData: ClientDataSteps = {
          [currentNavigationState.currentStep]: {
            ...(currentStepData as Record<string, unknown>),
            lastUpdate: new Date().toISOString(),
          },
        }

        updatedFormData = {
          steps: {
            ...formData.steps,
            ...formattedStepData,
          },
          navigationState: updatedNavigationState,
        }
      } else {
        updatedFormData = {
          ...formData,
          navigationState: updatedNavigationState,
        }
      }

      executeSaveData({
        formData: updatedFormData,
      })

      return { success: true, nextStep: nextStep.title }
    } catch (error) {
      console.error('❌ [useDataManagement] Error in goToNextStep:', error)
      return { success: false, error }
    }
  }

  const goToPreviousStep = async () => {
    try {
      if (!currentNavigationState?.navigationSteps) {
        console.error('❌ [goToPreviousStep] Navigation steps not available')
        return { success: false, error: 'Navigation state not available' }
      }

      const currentStepIndex = currentNavigationState.navigationSteps.findIndex(
        (step) => step.title === currentNavigationState.currentStep
      )

      const previousStepIndex = currentStepIndex - 1

      if (previousStepIndex < 0) {
        console.log('📝 [useDataManagement] Already at first step')
        return { success: false, error: 'Already at first step' }
      }

      const previousStep = currentNavigationState.navigationSteps[previousStepIndex]

      // Update navigation state for previous step
      const updatedSteps = currentNavigationState.navigationSteps.map((step, index) => ({
        ...step,
        isActive: index === previousStepIndex,
        // Don't change completion status when going back
      }))

      const updatedNavigationState: NavigationState = {
        currentStep: previousStep.title,
        navigationSteps: updatedSteps,
      }

      const updatedFormData: ClientDataManagement = {
        ...formData,
        navigationState: updatedNavigationState,
      }

      console.log('⬅️ [useDataManagement] Moving to previous step:', {
        from: currentNavigationState.currentStep,
        to: previousStep.title,
        preservedStepsCount: updatedSteps.length,
        allFormSteps: Object.keys(formData.steps || {}),
      })

      executeSaveData({
        formData: updatedFormData,
      })

      return { success: true, previousStep: previousStep.title }
    } catch (error) {
      console.error('❌ [useDataManagement] Error in goToPreviousStep:', error)
      return { success: false, error }
    }
  }

  const isFirstStep = useMemo(() => {
    if (!currentNavigationState?.navigationSteps) {
      return false
    }

    const currentStepIndex = currentNavigationState.navigationSteps.findIndex(
      (step) => step.title === currentNavigationState.currentStep
    )

    const isFirst = currentStepIndex === 0

    return isFirst
  }, [currentNavigationState])

  const isLastStep = useMemo(() => {
    if (!currentNavigationState?.navigationSteps) return false
    const currentStepIndex = currentNavigationState.navigationSteps.findIndex(
      (step) => step.title === currentNavigationState.currentStep
    )
    return currentStepIndex === currentNavigationState.navigationSteps.length - 1
  }, [currentNavigationState])

  /**
   * Check if all steps are completed
   */
  const isFormComplete = useMemo(() => {
    if (!currentNavigationState?.navigationSteps) return false
    return currentNavigationState.navigationSteps.every((step) => step.isCompleted)
  }, [currentNavigationState])

  const currentStepName = useMemo(() => {
    return currentNavigationState?.currentStep
  }, [currentNavigationState])

  /**
   * Save current step data and navigate to next step
   * This is the main function to call when user clicks "Next"
   */
  const saveAndGoToNextStep = async (currentStepData: unknown) => {
    try {
      if (!currentNavigationState?.navigationSteps) {
        return { success: false, error: 'Navigation state not available' }
      }

      // First save the current step data
      const saveResult = await saveStepData(currentNavigationState.currentStep, currentStepData)

      if (!saveResult.success) {
        return saveResult
      }

      return saveResult
    } catch (error) {
      console.error('❌ [useDataManagement] Error in saveAndGoToNextStep:', error)
      return { success: false, error }
    }
  }

  /**
   * Complete the entire form - save current step and mark as DONE
   * This is called on the last step when user clicks "Complete"
   */
  const completeForm = async (currentStepData: unknown) => {
    try {
      if (!currentNavigationState?.navigationSteps) {
        return { success: false, error: 'Navigation state not available' }
      }

      // Prepare step data in the format expected by the API
      const formattedStepData: ClientDataSteps = {
        [currentNavigationState.currentStep]: {
          ...(currentStepData as Record<string, unknown>),
          lastUpdate: new Date().toISOString(),
        },
      }

      // Mark all steps as completed
      const completedSteps = currentNavigationState.navigationSteps.map((step) => ({
        ...step,
        isCompleted: true,
      }))

      const updatedNavigationState: NavigationState = {
        currentStep: currentNavigationState.currentStep,
        navigationSteps: completedSteps,
      }

      const updatedFormData: ClientDataManagement = {
        steps: {
          ...formData.steps,
          ...formattedStepData,
        },
        navigationState: updatedNavigationState,
      }

      console.log('🎯 [useDataManagement] Completing form:', {
        stepName: currentNavigationState.currentStep,
        allSteps: Object.keys(updatedFormData.steps),
        status: 'DONE',
      })

      // Send with status DONE to indicate form completion
      executeSaveData({
        formData: updatedFormData,
        status: 'DONE',
      })

      return { success: true, completed: true }
    } catch (error) {
      console.error('❌ [useDataManagement] Error in completeForm:', error)
      return { success: false, error }
    }
  }

  /**
   * Helper function to handle form submission with automatic last step detection
   * This eliminates the need for components to check isLastStep() manually
   */
  const handleFormSubmission = async (currentStepData: unknown) => {
    if (isLastStep) {
      return await completeForm(currentStepData)
    } else {
      return await saveAndGoToNextStep(currentStepData)
    }
  }

  return {
    // Actions
    saveStepData,
    goToNextStep,
    goToPreviousStep,
    handleFormSubmission, // Helper function that automatically handles last step logic

    // State
    isSaving: isSaving || isSavingWithValidation, // Combined loading state
    saveResult,
    saveWithValidationResult,

    // Utilities
    isLastStep,
    isFirstStep,
    isFormComplete,

    // Current navigation state for components
    currentNavigationState,
    currentStepName,
  }
}
