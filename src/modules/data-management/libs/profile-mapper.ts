import { ProfileFormInputs } from '@/modules/profile/types/profile-schema'

import { ClientDataSteps } from '../types/data-management-types'

/**
 * Maps data management form data to profile API format
 */
export function mapDataManagementToProfile(formData: ClientDataSteps): Partial<ProfileFormInputs> {
  const profileData: Partial<ProfileFormInputs> = {}

  // Client information
  if (formData.clientInformation) {
    Object.assign(profileData, formData.clientInformation)
  }

  // Legitimation
  if (formData.legitimation) {
    Object.assign(profileData, formData.legitimation)
  }

  // Address
  if (formData.defaultAddress) {
    Object.assign(profileData, formData.defaultAddress)
  }

  // Contact data
  if (formData.contactData) {
    Object.assign(profileData, formData.contactData)
  }

  return profileData
}

/**
 * Extracts profile-related steps from data management form
 */
export function extractProfileSteps(formData: ClientDataSteps): ClientDataSteps {
  const { clientInformation, legitimation, defaultAddress, contactData } = formData
  return { clientInformation, legitimation, defaultAddress, contactData }
}

/**
 * Checks if step is profile-related
 */
export function isProfileRelatedStep(stepName: string): boolean {
  return ['clientInformation', 'legitimation', 'defaultAddress', 'contactData'].includes(stepName)
}

/**
 * Checks if form has any profile data
 */
export function hasProfileData(formData: ClientDataSteps): boolean {
  return !!(formData.clientInformation || formData.legitimation || formData.defaultAddress || formData.contactData)
}
