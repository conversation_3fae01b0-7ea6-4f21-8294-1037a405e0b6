'use server'

import { revalidateTag } from 'next/cache'

import { createDetailedError, fetchApi } from '@/lib/fetch-api'
import { authenticatedAction } from '@/lib/safe-actions'

import { profileSchema } from '../types/profile-schema'

export const updateUserProfile = authenticatedAction
  .schema(profileSchema)
  .action(async ({ parsedInput, ctx: { session } }) => {
    if (!session?.accessToken) {
      throw new Error('No session found')
    }

    // Prepare profile data structure matching the API expectations
    const profileData = {
      salutation: parsedInput.salutation,
      firstName: parsedInput.firstName,
      lastName: parsedInput.lastName,
      birthdate: parsedInput.birthdate?.toISOString(),
      nationality: parsedInput.nationality,
      familyStatus: parsedInput.familyStatus,

      // Address structure
      address: {
        street: parsedInput.street,
        buildingNumber: parsedInput.streetNum,
        zip: parsedInput.zip,
        city: parsedInput.city,
        country: parsedInput.country,
      },

      // Communication channels structure
      communicationChannels: {
        EMAIL: {
          value: parsedInput.email,
          status: 'ACTIVE',
        },
        ...(parsedInput.phone && {
          PHONE: {
            value: parsedInput.phone,
            status: 'ACTIVE',
          },
        }),
        ...(parsedInput.mobile && {
          MOBILE: {
            value: parsedInput.mobile,
            status: 'ACTIVE',
          },
        }),
      },
    }

    // Update profile via API
    const response = await fetchApi(
      '/profile',
      {
        method: 'PUT',
        body: JSON.stringify({ profile: profileData }),
      },
      session
    )

    if (response.error) {
      throw createDetailedError(response.error, response.details)
    }

    revalidateTag('profile')

    return response.data
  })

export const revalidateProfile = async () => {
  return revalidateTag('profile')
}
