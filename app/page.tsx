"use client";
import ReExt from "@sencha/reext";
import { useRef, useState } from "react";
type FlexDirection = "row" | "row-reverse" | "column" | "column-reverse";
interface RowData {
  name: string;
  email: string;
  phone: string;
}

export default function Home() {
  const [row, setRow] = useState<RowData | null>(null);
  const [direction, setDirection] = useState<FlexDirection>("row");
  const directionRef = useRef<FlexDirection>(direction);
  directionRef.current = direction;

  return (
    <div style={{ flex: 1, display: "flex", flexDirection: "column" }}>
      <div
        style={{ borderBottom: "2px solid lightgray", margin: "0 0 10px 0" }}
      >
        <ReExt
          xtype="logo"
          style={{ width: 250, border: "2px solid blue", margin: "0 0 10px 0" }}
        />
      </div>
      <div
        style={{
          height: "30px",
          display: "flex",
          justifyContent: "flex-start",
          marginBottom: "10px",
        }}
      >
        <ReExt
          xtype="button"
          style={{ width: "300px", height: "30px", marginRight: "10px" }}
          config={{ text: direction + " is current", width: "100%" }}
          onClick={() => {
            if (directionRef.current === "row") {
              setDirection("column");
            } else {
              setDirection("row");
            }
          }}
        />
      </div>
      <div style={{ display: "flex", flexDirection: direction }}>
        <ReExt
          xtype="grid"
          style={{ width: "325px", height: "120px" }}
          config={{
            title: "grid",
            columns: [
              { text: "Name", dataIndex: "name", width: 60 },
              { text: "Email", dataIndex: "email", flex: 1 },
              { text: "Phone", dataIndex: "phone", width: 80 },
            ],

            store: {
              data: {
                items: [
                  {
                    name: "Jean Luc",
                    email: "<EMAIL>",
                    phone: "************",
                  },
                  {
                    name: "Worf",
                    email: "<EMAIL>",
                    phone: "************",
                  },
                  {
                    name: "Deanna",
                    email: "<EMAIL>",
                    phone: "************",
                  },
                  {
                    name: "Data",
                    email: "<EMAIL>",
                    phone: "************",
                  },
                ],
              },
              proxy: {
                type: "memory",
                reader: {
                  type: "json",
                  rootProperty: "items",
                },
              },
            },
          }}
          onSelect={(grid, selected) => {
            let row: RowData;
            if (selected[0] === undefined) {
              row = selected.data;
            } else {
              row = selected[0].data;
            }
            setRow(row);
          }}
        />
        <div
          style={{
            width: "300px",
            padding: "10px 10px 10px 13px",
            border: "1px solid gray",
          }}
        >
          <ReExt
            xtype="displayfield"
            style={{ border: "1px solid lightgray" }}
            config={{ value: `name: ${row === null ? "" : row.name}` }}
          />
          <ReExt
            xtype="displayfield"
            style={{ border: "1px solid lightgray" }}
            config={{ value: `email: ${row === null ? "" : row.email}` }}
          />
          <ReExt
            xtype="displayfield"
            style={{ border: "1px solid lightgray" }}
            config={{ value: `phone: ${row === null ? "" : row.phone}` }}
          />
        </div>
      </div>
    </div>
  );
}
