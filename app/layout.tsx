"use client";
import { ReExtData, ReExtProvider } from "@sencha/reext";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const ReExtData: ReExtData = {
    sdkversion: "7.8.0",
    toolkit: "classic",
    theme: "classic",
    packages: {
      charts: false,
      fontawesome: false,
      ux: false,
      calendar: false,
      d3: false,
      exporter: false,
      pivot: false,
      pivotd3: false,
      pivotlocale: false,
      froalaeditor: false,
    },
    rtl: false,
    locale: "en",
    debug: false,
    urlbase: "./",
    location: "remote",
    overrides: false,
  };
  console.log("RootLayout");

  return (
    <html lang="en">
      <body>
        <ReExtProvider ReExtData={ReExtData}>{children}</ReExtProvider>
      </body>
    </html>
  );
}
